//
//  CoreDataManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import Foundation
import CoreData
import CloudKit
import SwiftUI
import Combine

/**
 * Core Data管理器
 * 负责CloudKit同步状态监控和数据一致性管理
 */
@MainActor
class CoreDataManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = CoreDataManager()
    
    // MARK: - Published Properties
    @Published var cloudKitStatus: CloudKitSyncStatus = .notStarted
    @Published var isSyncing: Bool = false
    @Published var lastSyncDate: Date?
    @Published var syncError: Error?
    @Published var syncRetryCount: Int = 0
    @Published var dataIntegrityStatus: DataIntegrityStatus = .unknown

    // MARK: - Private Properties
    private let persistenceController = PersistenceController.shared
    private var cancellables = Set<AnyCancellable>()
    private var syncRetryTimer: Timer?
    private let maxRetryAttempts = 5
    private let retryInterval: TimeInterval = 30.0 // 30秒重试间隔
    
    // MARK: - Computed Properties
    
    /**
     * 获取视图上下文
     */
    var viewContext: NSManagedObjectContext {
        return persistenceController.container.viewContext
    }
    
    /**
     * 获取CloudKit同步状态
     */
    var cloudKitSyncEnabled: Bool {
        return persistenceController.isCloudKitEnabled
    }
    
    // MARK: - Initialization
    
    private init() {
        setupCloudKitNotifications()
        setupInitialState()
    }
    
    // MARK: - Setup Methods
    
    /**
     * 设置CloudKit通知监听
     */
    private func setupCloudKitNotifications() {
        // 监听CloudKit导入通知
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleCloudKitImport()
            }
        }
        
        // 监听CloudKit导出通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("NSPersistentStoreDidImportUbiquitousContentChanges"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleCloudKitExport()
            }
        }
    }
    
    /**
     * 设置初始状态
     */
    private func setupInitialState() {
        cloudKitStatus = .syncCompleted
        lastSyncDate = UserDefaults.standard.object(forKey: "last_cloudkit_sync_date") as? Date
        
        print("☁️ CoreDataManager初始化完成，CloudKit同步已启用")
    }
    
    // MARK: - CloudKit Sync Handlers
    
    /**
     * 处理CloudKit导入通知
     */
    private func handleCloudKitImport() {
        print("📥 CloudKit数据导入中...")
        
        cloudKitStatus = .syncInProgress
        isSyncing = true
        
        viewContext.perform {
            self.viewContext.refreshAllObjects()
            
            // 执行数据一致性检查和修复
            self.performDataConsistencyCheck()
            
            DispatchQueue.main.async {
                self.cloudKitStatus = .syncCompleted
                self.isSyncing = false
                self.lastSyncDate = Date()
                self.updateLastSyncDate()
                print("✅ CloudKit数据导入完成")
                
                // 发送同步完成通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("CloudKitSyncCompleted"),
                    object: nil
                )
            }
        }
    }
    
    /**
     * 处理CloudKit导出通知
     */
    private func handleCloudKitExport() {
        print("📤 CloudKit数据导出中...")
        
        cloudKitStatus = .syncInProgress
        isSyncing = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.cloudKitStatus = .syncCompleted
            self.isSyncing = false
            self.lastSyncDate = Date()
            self.updateLastSyncDate()
            print("✅ CloudKit数据导出完成")
        }
    }
    
    // MARK: - Data Management
    
    /**
     * 保存上下文
     */
    func save() {
        persistenceController.save()
    }
    
    /**
     * 获取当前用户
     */
    func getCurrentUser() -> User? {
        return persistenceController.getCurrentUser()
    }
    
    /**
     * 手动触发CloudKit同步
     */
    func triggerCloudKitSync() {
        guard cloudKitSyncEnabled else {
            print("⚠️ CloudKit同步未启用")
            return
        }
        
        print("🔄 手动触发CloudKit同步...")
        
        cloudKitStatus = .syncInProgress
        isSyncing = true
        
        // 强制保存到CloudKit
        persistenceController.triggerCloudKitSync()
        
        // 刷新所有对象以获取最新的CloudKit数据
        viewContext.refreshAllObjects()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.cloudKitStatus = .syncCompleted
            self.isSyncing = false
            self.lastSyncDate = Date()
            self.updateLastSyncDate()
            print("✅ 手动CloudKit同步完成")
        }
    }
    
    // MARK: - Data Consistency
    
    /**
     * 执行数据一致性检查和修复
     */
    private func performDataConsistencyCheck() {
        print("🔍 执行数据一致性检查...")
        
        // 检查用户数据完整性
        checkUserDataIntegrity()
        
        // 检查成员数据完整性
        checkMemberDataIntegrity()
        
        // 检查积分记录完整性
        checkPointRecordIntegrity()
        
        print("✅ 数据一致性检查完成")
    }
    
    /**
     * 检查用户数据完整性
     */
    private func checkUserDataIntegrity() {
        let request: NSFetchRequest<User> = User.fetchRequest()
        
        do {
            let users = try viewContext.fetch(request)
            for user in users {
                // 确保用户有订阅信息
                if user.subscription == nil {
                    print("⚠️ 用户 \(user.nickname ?? "未知") 缺少订阅信息，正在修复...")
                    // 创建默认免费订阅
                    let subscription = Subscription(context: viewContext)
                    subscription.id = UUID()
                    subscription.subscriptionType = "free"
                    subscription.isActive = true
                    subscription.createdAt = Date()
                    subscription.updatedAt = Date()
                    subscription.user = user
                }
            }
        } catch {
            print("❌ 检查用户数据完整性失败: \(error)")
        }
    }
    
    /**
     * 检查成员数据完整性
     */
    private func checkMemberDataIntegrity() {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        
        do {
            let members = try viewContext.fetch(request)
            for member in members {
                // 确保成员有用户关联
                if member.user == nil {
                    print("⚠️ 成员 \(member.name ?? "未知") 缺少用户关联")
                }
                
                // 确保成员有有效的积分值
                if member.currentPoints < 0 {
                    print("⚠️ 成员 \(member.name ?? "未知") 积分为负数，正在修复...")
                    member.currentPoints = 0
                }
            }
        } catch {
            print("❌ 检查成员数据完整性失败: \(error)")
        }
    }
    
    /**
     * 检查积分记录完整性
     */
    private func checkPointRecordIntegrity() {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        
        do {
            let records = try viewContext.fetch(request)
            for record in records {
                // 确保积分记录有成员关联
                if record.member == nil {
                    print("⚠️ 发现孤立的积分记录，正在删除...")
                    viewContext.delete(record)
                }
            }
        } catch {
            print("❌ 检查积分记录完整性失败: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    
    /**
     * 更新最后同步时间
     */
    private func updateLastSyncDate() {
        UserDefaults.standard.set(lastSyncDate, forKey: "last_cloudkit_sync_date")
    }

    // MARK: - Reset Methods

    /**
     * 重置同步状态
     * 用于清除所有数据后的状态重置
     */
    func resetSyncStatus() {
        print("🔄 重置CoreDataManager同步状态...")

        // 重置发布的属性
        cloudKitStatus = .notStarted
        isSyncing = false
        lastSyncDate = nil
        syncError = nil

        // 取消所有观察者
        cancellables.removeAll()

        // 重新设置通知观察者
        setupCloudKitNotifications()

        // 重新设置初始状态
        setupInitialState()

        print("✅ CoreDataManager同步状态重置完成")
    }

    // MARK: - Enhanced Sync Monitoring

    /**
     * 检查数据完整性
     */
    func checkDataIntegrity() async {
        print("🔍 检查数据完整性...")

        dataIntegrityStatus = .checking

        let context = viewContext
        await context.perform {
            var issues: [DataIntegrityIssue] = []

            // 检查孤立的积分记录
            let pointRecordRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            pointRecordRequest.predicate = NSPredicate(format: "member == nil")

            if let orphanedRecords = try? context.fetch(pointRecordRequest), !orphanedRecords.isEmpty {
                issues.append(.orphanedPointRecords(count: orphanedRecords.count))

                // 清理孤立记录
                for record in orphanedRecords {
                    context.delete(record)
                }
            }

            // 检查成员积分一致性
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            if let members = try? context.fetch(memberRequest) {
                for member in members {
                    let calculatedPoints = member.pointRecords?.allObjects.compactMap { $0 as? PointRecord }
                        .reduce(0) { $0 + Int($1.value) } ?? 0

                    if calculatedPoints != Int(member.currentPoints) {
                        issues.append(.pointMismatch(memberName: member.name ?? "未知",
                                                   calculated: calculatedPoints,
                                                   stored: Int(member.currentPoints)))

                        // 修复积分不一致
                        member.currentPoints = Int32(calculatedPoints)
                    }
                }
            }

            // 保存修复
            if context.hasChanges {
                try? context.save()
            }

            DispatchQueue.main.async {
                if issues.isEmpty {
                    self.dataIntegrityStatus = .healthy
                    print("✅ 数据完整性检查通过")
                } else {
                    self.dataIntegrityStatus = .issuesFixed
                    print("⚠️ 发现并修复了 \(issues.count) 个数据完整性问题")
                    for issue in issues {
                        print("   - \(issue.description)")
                    }
                }
            }
        }
    }

    /**
     * 强制同步重试
     */
    func forceSyncRetry() async {
        print("🔄 强制同步重试...")

        syncRetryCount = 0
        syncError = nil

        await retrySyncOperation()
    }

    /**
     * 获取同步状态摘要
     */
    func getSyncStatusSummary() -> SyncStatusSummary {
        return SyncStatusSummary(
            status: cloudKitStatus,
            lastSyncDate: lastSyncDate,
            retryCount: syncRetryCount,
            dataIntegrity: dataIntegrityStatus,
            errorMessage: syncError?.localizedDescription
        )
    }

    // MARK: - Private Enhanced Methods

    /**
     * 处理CloudKit同步错误
     */
    private func handleCloudKitError(_ error: Error) {
        print("❌ CloudKit同步错误: \(error)")

        cloudKitStatus = .syncFailed
        isSyncing = false
        syncError = error

        // 启动重试机制
        if syncRetryCount < maxRetryAttempts {
            startSyncRetryTimer()
        } else {
            print("❌ CloudKit同步重试次数已达上限")
            dataIntegrityStatus = .compromised
        }
    }

    /**
     * 启动同步重试定时器
     */
    private func startSyncRetryTimer() {
        stopSyncRetryTimer()

        syncRetryCount += 1
        print("🔄 启动CloudKit同步重试 (\(syncRetryCount)/\(maxRetryAttempts))")

        syncRetryTimer = Timer.scheduledTimer(withTimeInterval: retryInterval, repeats: false) { [weak self] _ in
            Task { @MainActor in
                await self?.retrySyncOperation()
            }
        }
    }

    /**
     * 停止同步重试定时器
     */
    private func stopSyncRetryTimer() {
        syncRetryTimer?.invalidate()
        syncRetryTimer = nil
    }

    /**
     * 重试同步操作
     */
    private func retrySyncOperation() async {
        print("🔄 重试CloudKit同步...")

        cloudKitStatus = .syncInProgress
        isSyncing = true

        // 触发保存以重新启动同步
        save()

        // 等待同步完成
        try? await Task.sleep(nanoseconds: 3_000_000_000)

        // 检查同步结果
        await checkSyncResult()
    }

    /**
     * 检查同步结果
     */
    private func checkSyncResult() async {
        // 检查是否还有待同步的数据
        let hasChanges = viewContext.hasChanges

        if hasChanges {
            // 仍有未同步的数据，继续重试
            if syncRetryCount < maxRetryAttempts {
                startSyncRetryTimer()
            } else {
                cloudKitStatus = .syncFailed
                isSyncing = false
                dataIntegrityStatus = .compromised
            }
        } else {
            // 同步成功
            cloudKitStatus = .syncCompleted
            isSyncing = false
            lastSyncDate = Date()
            updateLastSyncDate()
            syncRetryCount = 0
            stopSyncRetryTimer()

            await checkDataIntegrity()
        }
    }
}

// MARK: - CloudKit Sync Status Enum

enum CloudKitSyncStatus {
    case notStarted
    case syncInProgress
    case syncCompleted
    case syncFailed

    var displayText: String {
        switch self {
        case .notStarted:
            return "未开始"
        case .syncInProgress:
            return "同步中"
        case .syncCompleted:
            return "同步完成"
        case .syncFailed:
            return "同步失败"
        }
    }

    var iconName: String {
        switch self {
        case .notStarted:
            return "icloud"
        case .syncInProgress:
            return "icloud.and.arrow.up"
        case .syncCompleted:
            return "icloud.and.arrow.up.fill"
        case .syncFailed:
            return "icloud.slash"
        }
    }
}

// MARK: - Enhanced Data Models

enum DataIntegrityStatus {
    case unknown
    case checking
    case healthy
    case issuesFixed
    case compromised

    var description: String {
        switch self {
        case .unknown: return "未知"
        case .checking: return "检查中"
        case .healthy: return "健康"
        case .issuesFixed: return "问题已修复"
        case .compromised: return "数据受损"
        }
    }
}

enum DataIntegrityIssue {
    case orphanedPointRecords(count: Int)
    case pointMismatch(memberName: String, calculated: Int, stored: Int)

    var description: String {
        switch self {
        case .orphanedPointRecords(let count):
            return "发现 \(count) 条孤立的积分记录"
        case .pointMismatch(let memberName, let calculated, let stored):
            return "\(memberName) 的积分不一致：计算值 \(calculated)，存储值 \(stored)"
        }
    }
}

struct SyncStatusSummary {
    let status: CloudKitSyncStatus
    let lastSyncDate: Date?
    let retryCount: Int
    let dataIntegrity: DataIntegrityStatus
    let errorMessage: String?
}
