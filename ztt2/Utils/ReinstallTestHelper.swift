//
//  ReinstallTestHelper.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import SwiftUI

/**
 * 重新安装测试助手
 * 用于测试重新安装后的CloudKit数据恢复功能
 */
class ReinstallTestHelper: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ReinstallTestHelper()
    
    // MARK: - Published Properties
    @Published var isTestMode: Bool = false
    @Published var testResults: [String] = []
    
    // MARK: - Private Properties
    private let userDefaults = UserDefaults.standard
    private let keychainManager = KeychainManager.shared
    
    private init() {}
    
    // MARK: - Public Methods
    
    /**
     * 模拟重新安装场景
     * 清除UserDefaults中的启动标记，但保留Keychain中的登录信息
     */
    func simulateReinstall() {
        print("🧪 开始模拟重新安装场景...")
        
        // 清除UserDefaults中的启动标记
        userDefaults.removeObject(forKey: "HasLaunchedBefore")
        
        // 记录测试结果
        addTestResult("✅ 已清除UserDefaults启动标记")
        
        // 检查Keychain中是否有登录信息
        let hasKeychainLogin = keychainManager.isLoggedIn()
        let appleUserID = keychainManager.getAppleUserID()
        
        if hasKeychainLogin && appleUserID != nil {
            addTestResult("✅ Keychain中保留登录信息: \(appleUserID!)")
            addTestResult("🔄 重新安装模拟完成，下次启动将触发数据恢复")
        } else {
            addTestResult("❌ Keychain中没有登录信息，无法模拟重新安装")
        }
        
        print("🧪 重新安装模拟完成")
    }
    
    /**
     * 检查重新安装检测是否正常工作
     */
    func testReinstallDetection() -> Bool {
        print("🧪 测试重新安装检测...")
        
        let hasLaunchedBefore = userDefaults.bool(forKey: "HasLaunchedBefore")
        let hasKeychainLogin = keychainManager.isLoggedIn()
        
        addTestResult("UserDefaults启动标记: \(hasLaunchedBefore)")
        addTestResult("Keychain登录状态: \(hasKeychainLogin)")
        
        // 重新安装的条件：UserDefaults中没有启动标记，但Keychain中有登录信息
        let isReinstall = !hasLaunchedBefore && hasKeychainLogin
        
        addTestResult("重新安装检测结果: \(isReinstall)")
        
        return isReinstall
    }
    
    /**
     * 重置测试环境
     */
    func resetTestEnvironment() {
        print("🧪 重置测试环境...")
        
        // 清除所有测试相关的数据
        userDefaults.removeObject(forKey: "HasLaunchedBefore")
        
        // 清除测试结果
        testResults.removeAll()
        isTestMode = false
        
        addTestResult("✅ 测试环境已重置")
        
        print("🧪 测试环境重置完成")
    }
    
    /**
     * 启用测试模式
     */
    func enableTestMode() {
        isTestMode = true
        testResults.removeAll()
        addTestResult("🧪 测试模式已启用")
    }
    
    /**
     * 禁用测试模式
     */
    func disableTestMode() {
        isTestMode = false
        addTestResult("🧪 测试模式已禁用")
    }
    
    /**
     * 获取当前状态报告
     */
    func getStatusReport() -> String {
        var report = "=== 重新安装检测状态报告 ===\n"
        
        let hasLaunchedBefore = userDefaults.bool(forKey: "HasLaunchedBefore")
        let hasKeychainLogin = keychainManager.isLoggedIn()
        let appleUserID = keychainManager.getAppleUserID()
        
        report += "UserDefaults启动标记: \(hasLaunchedBefore)\n"
        report += "Keychain登录状态: \(hasKeychainLogin)\n"
        report += "Apple用户ID: \(appleUserID ?? "无")\n"
        report += "重新安装检测: \(!hasLaunchedBefore && hasKeychainLogin)\n"
        
        return report
    }
    
    // MARK: - Private Methods
    
    /**
     * 添加测试结果
     */
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let resultWithTime = "[\(timestamp)] \(result)"
        
        DispatchQueue.main.async {
            self.testResults.append(resultWithTime)
        }
        
        print("🧪 \(resultWithTime)")
    }
}

// MARK: - Test View

/**
 * 重新安装测试视图
 * 用于开发和调试时测试重新安装功能
 */
struct ReinstallTestView: View {
    @StateObject private var testHelper = ReinstallTestHelper.shared
    @State private var showStatusReport = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 测试模式状态
                HStack {
                    Text("测试模式:")
                    Spacer()
                    Text(testHelper.isTestMode ? "已启用" : "已禁用")
                        .foregroundColor(testHelper.isTestMode ? .green : .gray)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
                
                // 测试按钮
                VStack(spacing: 12) {
                    Button("启用测试模式") {
                        testHelper.enableTestMode()
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Button("模拟重新安装") {
                        testHelper.simulateReinstall()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("测试重新安装检测") {
                        _ = testHelper.testReinstallDetection()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("查看状态报告") {
                        showStatusReport = true
                    }
                    .buttonStyle(.bordered)
                    
                    Button("重置测试环境") {
                        testHelper.resetTestEnvironment()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
                
                // 测试结果
                if !testHelper.testResults.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("测试结果:")
                            .font(.headline)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 4) {
                                ForEach(testHelper.testResults, id: \.self) { result in
                                    Text(result)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.05))
                    .cornerRadius(8)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("重新安装测试")
            .alert("状态报告", isPresented: $showStatusReport) {
                Button("确定") { }
            } message: {
                Text(testHelper.getStatusReport())
            }
        }
    }
}

#Preview {
    ReinstallTestView()
}
