//
//  ReinstallRecoveryTests.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import XCTest
import CloudKit
@testable import ztt2

/**
 * 重新安装数据恢复功能测试
 * 验证重新安装检测和CloudKit数据恢复功能
 */
class ReinstallRecoveryTests: XCTestCase {
    
    var authManager: AuthenticationManager!
    var keychainManager: KeychainManager!
    var userDefaults: UserDefaults!
    
    override func setUpWithError() throws {
        // 使用测试专用的UserDefaults
        userDefaults = UserDefaults(suiteName: "test.ztt2.reinstall")
        authManager = AuthenticationManager.shared
        keychainManager = KeychainManager.shared
    }
    
    override func tearDownWithError() throws {
        // 清理测试数据
        userDefaults.removePersistentDomain(forName: "test.ztt2.reinstall")
        authManager = nil
        keychainManager = nil
        userDefaults = nil
    }
    
    // MARK: - 重新安装检测测试
    
    /**
     * 测试首次安装场景
     */
    func testFirstInstallDetection() throws {
        // 清除所有标记
        userDefaults.removeObject(forKey: "HasLaunchedBefore")
        keychainManager.clearLoginInfo()
        
        // 模拟首次启动
        let hasLaunchedBefore = userDefaults.bool(forKey: "HasLaunchedBefore")
        let hasKeychainLogin = keychainManager.isLoggedIn()
        
        XCTAssertFalse(hasLaunchedBefore, "首次安装应该没有启动标记")
        XCTAssertFalse(hasKeychainLogin, "首次安装应该没有登录信息")
        
        // 首次安装不应该被检测为重新安装
        let isReinstall = !hasLaunchedBefore && hasKeychainLogin
        XCTAssertFalse(isReinstall, "首次安装不应该被检测为重新安装")
    }
    
    /**
     * 测试正常使用场景
     */
    func testNormalUsageDetection() throws {
        // 模拟正常使用状态
        userDefaults.set(true, forKey: "HasLaunchedBefore")
        keychainManager.saveLoginInfo(appleUserID: "test.user.id", userName: "测试用户")
        
        let hasLaunchedBefore = userDefaults.bool(forKey: "HasLaunchedBefore")
        let hasKeychainLogin = keychainManager.isLoggedIn()
        
        XCTAssertTrue(hasLaunchedBefore, "正常使用应该有启动标记")
        XCTAssertTrue(hasKeychainLogin, "正常使用应该有登录信息")
        
        // 正常使用不应该被检测为重新安装
        let isReinstall = !hasLaunchedBefore && hasKeychainLogin
        XCTAssertFalse(isReinstall, "正常使用不应该被检测为重新安装")
    }
    
    /**
     * 测试重新安装场景
     */
    func testReinstallDetection() throws {
        // 模拟重新安装状态：Keychain有登录信息，但UserDefaults没有启动标记
        userDefaults.removeObject(forKey: "HasLaunchedBefore")
        keychainManager.saveLoginInfo(appleUserID: "test.user.id", userName: "测试用户")
        
        let hasLaunchedBefore = userDefaults.bool(forKey: "HasLaunchedBefore")
        let hasKeychainLogin = keychainManager.isLoggedIn()
        
        XCTAssertFalse(hasLaunchedBefore, "重新安装应该没有启动标记")
        XCTAssertTrue(hasKeychainLogin, "重新安装应该保留登录信息")
        
        // 应该被检测为重新安装
        let isReinstall = !hasLaunchedBefore && hasKeychainLogin
        XCTAssertTrue(isReinstall, "应该被检测为重新安装")
    }
    
    // MARK: - Keychain 持久性测试
    
    /**
     * 测试Keychain数据持久性
     */
    func testKeychainPersistence() throws {
        let testUserID = "test.persistence.user"
        let testUserName = "持久性测试用户"
        let testEmail = "<EMAIL>"
        
        // 保存登录信息
        keychainManager.saveLoginInfo(
            appleUserID: testUserID,
            userName: testUserName,
            userEmail: testEmail
        )
        
        // 验证保存成功
        XCTAssertTrue(keychainManager.isLoggedIn(), "登录状态应该被保存")
        XCTAssertEqual(keychainManager.getAppleUserID(), testUserID, "Apple用户ID应该被保存")
        XCTAssertEqual(keychainManager.getUserName(), testUserName, "用户名应该被保存")
        XCTAssertEqual(keychainManager.getUserEmail(), testEmail, "邮箱应该被保存")
        
        // 模拟应用重启（UserDefaults清除，Keychain保留）
        userDefaults.removePersistentDomain(forName: "test.ztt2.reinstall")
        
        // 验证Keychain数据仍然存在
        XCTAssertTrue(keychainManager.isLoggedIn(), "重启后登录状态应该保留")
        XCTAssertEqual(keychainManager.getAppleUserID(), testUserID, "重启后Apple用户ID应该保留")
    }
    
    // MARK: - CloudKit 可用性测试
    
    /**
     * 测试CloudKit容器配置
     */
    func testCloudKitContainerConfiguration() throws {
        let containerIdentifier = "iCloud.com.rainkygong.ztt2"
        let container = CKContainer(identifier: containerIdentifier)
        
        XCTAssertNotNil(container, "CloudKit容器应该能够正确初始化")
        XCTAssertEqual(container.containerIdentifier, containerIdentifier, "容器标识符应该正确")
    }
    
    /**
     * 测试CloudKit账户状态检查（异步）
     */
    func testCloudKitAccountStatus() async throws {
        let container = CKContainer(identifier: "iCloud.com.rainkygong.ztt2")
        
        do {
            let status = try await container.accountStatus()
            
            // 在测试环境中，账户状态可能是任何值，我们只验证调用不会崩溃
            XCTAssertTrue([
                CKAccountStatus.available,
                CKAccountStatus.noAccount,
                CKAccountStatus.restricted,
                CKAccountStatus.couldNotDetermine,
                CKAccountStatus.temporarilyUnavailable
            ].contains(status), "账户状态应该是有效的枚举值")
            
            print("CloudKit账户状态: \(status)")
            
        } catch {
            // 在测试环境中，CloudKit可能不可用，这是正常的
            print("CloudKit账户状态检查失败（测试环境正常）: \(error)")
        }
    }
    
    // MARK: - 集成测试
    
    /**
     * 测试完整的重新安装检测流程
     */
    func testCompleteReinstallFlow() throws {
        // 1. 模拟重新安装状态
        userDefaults.removeObject(forKey: "HasLaunchedBefore")
        keychainManager.saveLoginInfo(appleUserID: "test.flow.user", userName: "流程测试用户")
        
        // 2. 验证重新安装检测
        let hasLaunchedBefore = userDefaults.bool(forKey: "HasLaunchedBefore")
        let hasKeychainLogin = keychainManager.isLoggedIn()
        let isReinstall = !hasLaunchedBefore && hasKeychainLogin
        
        XCTAssertTrue(isReinstall, "应该检测到重新安装")
        
        // 3. 模拟设置启动标记
        userDefaults.set(true, forKey: "HasLaunchedBefore")
        
        // 4. 验证后续启动不会再检测为重新安装
        let hasLaunchedAfter = userDefaults.bool(forKey: "HasLaunchedBefore")
        let isReinstallAfter = !hasLaunchedAfter && hasKeychainLogin
        
        XCTAssertFalse(isReinstallAfter, "设置启动标记后不应该再检测为重新安装")
    }
    
    // MARK: - 性能测试
    
    /**
     * 测试重新安装检测的性能
     */
    func testReinstallDetectionPerformance() throws {
        // 准备测试数据
        keychainManager.saveLoginInfo(appleUserID: "performance.test.user")
        
        measure {
            // 测试重新安装检测的性能
            for _ in 0..<1000 {
                let hasLaunchedBefore = userDefaults.bool(forKey: "HasLaunchedBefore")
                let hasKeychainLogin = keychainManager.isLoggedIn()
                let _ = !hasLaunchedBefore && hasKeychainLogin
            }
        }
    }
}

// MARK: - 测试助手扩展

extension ReinstallRecoveryTests {
    
    /**
     * 清理测试环境
     */
    func cleanupTestEnvironment() {
        userDefaults.removeObject(forKey: "HasLaunchedBefore")
        keychainManager.clearLoginInfo()
    }
    
    /**
     * 设置重新安装测试环境
     */
    func setupReinstallTestEnvironment() {
        userDefaults.removeObject(forKey: "HasLaunchedBefore")
        keychainManager.saveLoginInfo(appleUserID: "test.reinstall.user", userName: "重新安装测试用户")
    }
}
