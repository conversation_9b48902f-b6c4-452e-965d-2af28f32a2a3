//
//  ztt2App.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI
import RevenueCat

@main
struct ztt2App: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    @StateObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var subscriptionSyncManager = SubscriptionSyncManager.shared
    @StateObject private var trialManager = TrialManager.shared
    @StateObject private var dataProtectionManager = DataProtectionManager.shared
    @StateObject private var dataBackupManager = DataBackupManager.shared
    @StateObject private var coreDataManager = CoreDataManager.shared
    @State private var isInitializing = true
    @State private var isDataRecoveryInProgress = false

    init() {
        print("🚀 应用启动 - 统一CloudKit多设备同步模式")

        // 初始化API密钥
        setupAPIKey()

        // 配置RevenueCat
        configureRevenueCat()
    }

    var body: some Scene {
        WindowGroup {
            Group {
                if isInitializing {
                    EnhancedLaunchScreenView()
                        .onAppear {
                            initializeApp()
                        }
                } else if isDataRecoveryInProgress {
                    DataRecoveryView()
                        .environmentObject(dataBackupManager)
                        .environmentObject(dataProtectionManager)
                } else {
                    ContentView()
                        .environment(\.managedObjectContext, persistenceController.container.viewContext)
                        .environmentObject(dataManager)
                        .environmentObject(authManager)
                        .environmentObject(revenueCatManager)
                        .environmentObject(subscriptionService)
                        .environmentObject(subscriptionSyncManager)
                        .environmentObject(trialManager)
                        .environmentObject(dataProtectionManager)
                        .environmentObject(dataBackupManager)
                        .environmentObject(coreDataManager)
                }
            }
        }
    }

    // MARK: - Private Methods

    /**
     * 设置API密钥
     */
    private func setupAPIKey() {
        let keychainManager = KeychainManager.shared

        // 如果Keychain中没有API密钥，则设置默认密钥
        if !keychainManager.hasAPIKey() {
            keychainManager.saveAPIKey("sk-eb2dc94c4f594097b7747421169b9110")
            print("🔐 已设置默认API密钥")
        } else {
            print("🔐 API密钥已存在于Keychain中")
        }
    }

    /**
     * 配置RevenueCat
     */
    private func configureRevenueCat() {
        // ⚠️ 重要：请将此API Key替换为您从RevenueCat Dashboard获取的真实API Key
        // 1. 登录 https://app.revenuecat.com/
        // 2. 创建新项目或选择现有项目
        // 3. 在项目设置中找到 API Keys
        // 4. 复制 Apple App Store 的 Public API Key
        // 5. 将下面的 "appl_YOUR_REVENUECAT_API_KEY_HERE" 替换为真实的API Key
        let apiKey = "appl_AoQsgjVJvNmhqMzpYtObdNflzsn"

        // 获取当前用户ID (如果已登录)
        let userId = AuthenticationManager.shared.currentUser?.appleUserID

        // 配置RevenueCat
        Task { @MainActor in
            RevenueCatManager.shared.configure(apiKey: apiKey, userId: userId)

            // 配置订阅服务
            SubscriptionService.shared.configure(userId: userId)
        }

        print("🔧 RevenueCat配置完成")
    }

    /**
     * 初始化应用
     */
    private func initializeApp() {
        // 立即开始登录状态检查，避免在ContentView中再次触发
        authManager.checkLoginStatus()

        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { // 增加时间让Logo旋转动画完成
            Task {
                await self.performDataRecoveryCheck()
            }
        }
    }

    /**
     * 执行数据恢复检查
     */
    private func performDataRecoveryCheck() async {
        print("🔍 开始数据恢复检查...")

        // 1. 初始化数据保护管理器
        await dataProtectionManager.initialize()

        // 2. 检查是否需要数据恢复
        let quickValidation = await DataConsistencyValidator.shared.performQuickValidation()

        if !quickValidation.isHealthy {
            print("⚠️ 检测到数据问题，启动数据恢复流程...")
            isDataRecoveryInProgress = true

            // 3. 尝试数据恢复
            await dataBackupManager.checkAndRestoreLostData()

            // 4. 等待恢复完成
            try? await Task.sleep(nanoseconds: 3_000_000_000)

            isDataRecoveryInProgress = false
        }

        // 5. 刷新试用状态，确保状态正确
        print("🔄 刷新试用状态...")
        trialManager.refreshTrialStatus()

        // 6. 启动数据完整性检查
        await coreDataManager.checkDataIntegrity()

        print("✅ 应用初始化完成 - CloudKit同步已启用")
        isInitializing = false
    }
}
