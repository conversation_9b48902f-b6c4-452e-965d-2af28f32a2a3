//
//  DataRecoveryView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import SwiftUI

/**
 * 数据恢复界面
 * 在检测到数据丢失时显示，提供数据恢复进度和状态信息
 */
struct DataRecoveryView: View {
    @EnvironmentObject private var dataBackupManager: DataBackupManager
    @EnvironmentObject private var dataProtectionManager: DataProtectionManager
    @State private var animationOffset: CGFloat = 0
    
    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.1),
                    Color.purple.opacity(0.1)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 30) {
                Spacer()
                
                // 恢复图标
                recoveryIcon
                
                // 标题和描述
                titleSection
                
                // 进度指示器
                progressSection
                
                // 状态信息
                statusSection
                
                Spacer()
                
                // 底部提示
                bottomTips
            }
            .padding(.horizontal, 40)
        }
        .onAppear {
            startAnimation()
        }
    }
    
    // MARK: - View Components
    
    private var recoveryIcon: some View {
        ZStack {
            Circle()
                .fill(Color.blue.opacity(0.2))
                .frame(width: 120, height: 120)
            
            Image(systemName: "icloud.and.arrow.down")
                .font(.system(size: 50, weight: .light))
                .foregroundColor(.blue)
                .offset(y: animationOffset)
                .animation(
                    Animation.easeInOut(duration: 2.0)
                        .repeatForever(autoreverses: true),
                    value: animationOffset
                )
        }
    }
    
    private var titleSection: some View {
        VStack(spacing: 12) {
            Text("数据恢复中")
                .font(.title)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text("正在从云端恢复您的数据，请稍候...")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
    }
    
    private var progressSection: some View {
        VStack(spacing: 16) {
            // 进度条
            ProgressView(value: dataBackupManager.restoreProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .scaleEffect(y: 2)
            
            // 进度百分比
            Text("\(Int(dataBackupManager.restoreProgress * 100))%")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.blue)
        }
        .padding(.horizontal, 20)
    }
    
    private var statusSection: some View {
        VStack(spacing: 12) {
            // 当前状态
            HStack {
                Image(systemName: statusIcon)
                    .foregroundColor(statusColor)
                    .font(.system(size: 16, weight: .medium))
                
                Text(statusText)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
            
            // 错误信息（如果有）
            if let errorMessage = dataBackupManager.errorMessage {
                HStack {
                    Image(systemName: "exclamationmark.triangle")
                        .foregroundColor(.orange)
                    
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(nil)
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange.opacity(0.1))
                )
            }
        }
    }
    
    private var bottomTips: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                    .font(.system(size: 14))
                
                Text("数据恢复过程中请保持网络连接")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            
            HStack {
                Image(systemName: "wifi")
                    .foregroundColor(.green)
                    .font(.system(size: 14))
                
                Text("确保已登录iCloud账户")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Computed Properties
    
    private var statusIcon: String {
        switch dataBackupManager.restoreStatus {
        case .idle:
            return "clock"
        case .checking:
            return "magnifyingglass"
        case .restoring:
            return "icloud.and.arrow.down"
        case .completed:
            return "checkmark.circle"
        case .failed:
            return "xmark.circle"
        }
    }
    
    private var statusColor: Color {
        switch dataBackupManager.restoreStatus {
        case .idle:
            return .gray
        case .checking:
            return .blue
        case .restoring:
            return .blue
        case .completed:
            return .green
        case .failed:
            return .red
        }
    }
    
    private var statusText: String {
        switch dataBackupManager.restoreStatus {
        case .idle:
            return "准备中..."
        case .checking:
            return "检查数据完整性..."
        case .restoring:
            return "正在恢复数据..."
        case .completed:
            return "数据恢复完成"
        case .failed:
            return "数据恢复失败"
        }
    }
    
    // MARK: - Private Methods
    
    private func startAnimation() {
        withAnimation {
            animationOffset = -10
        }
    }
}

// MARK: - Preview

struct DataRecoveryView_Previews: PreviewProvider {
    static var previews: some View {
        DataRecoveryView()
            .environmentObject(DataBackupManager.shared)
            .environmentObject(DataProtectionManager.shared)
    }
}
